###
Can only run on one server
use crontab for ever 30min
###
# TODO: more docs

TRBCol = COLLECTION 'rni','reso_treb_evow_merged'
BRECol = COLLECTION 'rni','bridge_bcre_merged'
DDFCol = COLLECTION 'rni','reso_crea_merged'
CARCol = COLLECTION 'rni','mls_car_master_records'
EDMCol = COLLECTION 'rni','mls_rae_master_records'
CLGCol = COLLECTION 'rni','mls_creb_master_records'

# TODO: key read from avgs, not cfg
main = ()->
  

main()