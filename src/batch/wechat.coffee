###
Can only run on one server
use crontab for ever 30min
###
# TODO: more docs

xlsx = require('xlsx')
path = require('path')

TRBCol = COLLECTION 'rni','reso_treb_evow_merged'
BRECol = COLLECTION 'rni','bridge_bcre_merged'
DDFCol = COLLECTION 'rni','reso_crea_merged'
CARCol = COLLECTION 'rni','mls_car_master_records'
EDMCol = COLLECTION 'rni','mls_rae_master_records'
CLGCol = COLLECTION 'rni','mls_creb_master_records'

debug = DEBUG()

# 定义表集合和对应的名称
COLLECTIONS = [
  { col: TRBCol, name: 'rni_TRB' }
  { col: BRECol, name: 'rni_BRE' }
  { col: DDFCol, name: 'rni_DDF' }
  { col: CARCol, name: 'rni_CAR' }
  { col: EDMCol, name: 'rni_EDM' }
  { col: CLGCol, name: 'rni_CLG' }
]

# 生成日期序列
generateDateRange = (startDateStr, endDateStr) ->
  dates = []
  # 使用年月日直接构造，避免时区问题
  [startYear, startMonth, startDay] = startDateStr.split('-').map(Number)
  [endYear, endMonth, endDay] = endDateStr.split('-').map(Number)

  currentDate = new Date(startYear, startMonth - 1, startDay) # 月份从0开始
  endDate = new Date(endYear, endMonth - 1, endDay)

  while currentDate <= endDate
    year = currentDate.getFullYear()
    month = String(currentDate.getMonth() + 1).padStart(2, '0')
    day = String(currentDate.getDate()).padStart(2, '0')
    dates.push("#{year}-#{month}-#{day}")
    currentDate.setDate(currentDate.getDate() + 1)
  return dates

# 统计单个表的数据
getCollectionStats = (collection) ->
  try
    pipeline = [
      {
        $match: {
          ts: {
            $gte: new Date('2025-07-01T00:00:00Z')
            $lte: new Date('2025-08-31T23:59:59Z')
          }
        }
      }
      {
        $group: {
          _id: {
            year: { $year: '$ts' }
            month: { $month: '$ts' }
            day: { $dayOfMonth: '$ts' }
          }
          count: { $sum: 1 }
        }
      }
      {
        $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
      }
    ]

    # 设置聚合选项，包括超时时间
    options = {
      maxTimeMS: 1800000  # 30分钟超时
      allowDiskUse: true  # 允许使用磁盘进行大数据量聚合
      cursor: { batchSize: 1000 }  # 设置批处理大小
    }

    # 使用cursor方式处理聚合结果，避免一次性加载所有数据到内存
    cursor = await collection.aggregate(pipeline, options)

    # 转换为日期字符串映射
    statsMap = {}
    for item in cursor
      year = item._id.year
      month = String(item._id.month).padStart(2, '0')
      day = String(item._id.day).padStart(2, '0')
      dateStr = "#{year}-#{month}-#{day}"
      statsMap[dateStr] = item.count

    return statsMap
  catch error
    if error.name is 'MongoNetworkTimeoutError' or error.message?.includes('timeout')
      debug.error 'MongoDB timeout error, retrying with smaller time range...'
      # 如果超时，尝试分月查询
      return await getCollectionStatsWithRetry(collection)
    else
      debug.error 'Error getting stats for collection:', error
      return {}

# 分月重试查询函数
getCollectionStatsWithRetry = (collection) ->
  try
    statsMap = {}

    # 分别查询7月和8月的数据
    months = [
      { start: new Date('2025-07-01T00:00:00Z'), end: new Date('2025-07-31T23:59:59Z') }
      { start: new Date('2025-08-01T00:00:00Z'), end: new Date('2025-08-31T23:59:59Z') }
    ]

    for monthRange in months
      pipeline = [
        {
          $match: {
            ts: {
              $gte: monthRange.start
              $lte: monthRange.end
            }
          }
        }
        {
          $group: {
            _id: {
              year: { $year: '$ts' }
              month: { $month: '$ts' }
              day: { $dayOfMonth: '$ts' }
            }
            count: { $sum: 1 }
          }
        }
        {
          $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
        }
      ]

      options = {
        maxTimeMS: 900000  # 15分钟超时
        allowDiskUse: true
        cursor: { batchSize: 500 }
      }

      cursor = collection.aggregate(pipeline, options)
      monthResult = await cursor.toArray()

      # 合并结果
      for item in monthResult
        year = item._id.year
        month = String(item._id.month).padStart(2, '0')
        day = String(item._id.day).padStart(2, '0')
        dateStr = "#{year}-#{month}-#{day}"
        statsMap[dateStr] = item.count

    return statsMap
  catch error
    debug.error 'Retry failed:', error
    return {}

# 生成Excel文件
generateExcel = (statsData, outputPath) ->
  try
    # 生成日期范围
    dateRange = generateDateRange('2025-07-01', '2025-08-31')

    # 创建表头
    headers = ['Date']
    for collection in COLLECTIONS
      headers.push(collection.name)

    # 创建数据行
    rows = [headers]

    for date in dateRange
      row = [date]
      for collection in COLLECTIONS
        count = statsData[collection.name]?[date] or 0
        row.push(count)
      rows.push(row)

    # 创建工作簿
    workbook = xlsx.utils.book_new()
    worksheet = xlsx.utils.aoa_to_sheet(rows)
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Daily Stats')

    # 写入文件
    xlsx.writeFile(workbook, outputPath)
    debug.info "Excel file generated successfully: #{outputPath}"

  catch error
    debug.error 'Error generating Excel file:', error
    throw error

# 主函数
main = ->
  debug.info 'Starting data collection for July and August 2025...'

  # 收集所有表的统计数据
  statsData = {}
  for collection in COLLECTIONS
    debug.info "Processing #{collection.name}..."
    startTime = Date.now()
    stats = await getCollectionStats(collection.col)
    endTime = Date.now()
    processingTime = Math.round((endTime - startTime) / 1000)
    statsData[collection.name] = stats
    daysCount = Object.keys(stats).length
    debug.info "#{collection.name} completed in #{processingTime}s, found #{daysCount} days"

  # 生成输出文件路径
  outputPath = path.join('/opt/appd5/rni_daily_stats.xlsx')

  # 生成Excel文件
  await generateExcel(statsData, outputPath)

  debug.info 'Process completed successfully!'
  EXIT 0

main()