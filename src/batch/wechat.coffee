###
Can only run on one server
use crontab for ever 30min
###
# TODO: more docs

xlsx = require('xlsx')
path = require('path')

TRBCol = COLLECTION 'rni','reso_treb_evow_merged'
BRECol = COLLECTION 'rni','bridge_bcre_merged'
DDFCol = COLLECTION 'rni','reso_crea_merged'
CARCol = COLLECTION 'rni','mls_car_master_records'
EDMCol = COLLECTION 'rni','mls_rae_master_records'
CLGCol = COLLECTION 'rni','mls_creb_master_records'

debug = DEBUG()

# 定义表集合和对应的名称
COLLECTIONS = [
  { col: TRBCol, name: 'rni_TRB' }
  { col: BRECol, name: 'rni_BRE' }
  { col: DDFCol, name: 'rni_DDF' }
  { col: CARCol, name: 'rni_CAR' }
  { col: EDMCol, name: 'rni_EDM' }
  { col: CLGCol, name: 'rni_CLG' }
]

# 生成日期序列
generateDateRange = (startDate, endDate) ->
  dates = []
  currentDate = new Date(startDate)
  while currentDate <= endDate
    year = currentDate.getFullYear()
    month = String(currentDate.getMonth() + 1).padStart(2, '0')
    day = String(currentDate.getDate()).padStart(2, '0')
    dates.push("#{year}-#{month}-#{day}")
    currentDate.setDate(currentDate.getDate() + 1)
  return dates

# 统计单个表的数据
getCollectionStats = (collection) ->
  try
    pipeline = [
      {
        $match: {
          ts: {
            $gte: new Date('2025-07-01T00:00:00Z')
            $lte: new Date('2025-08-31T23:59:59Z')
          }
        }
      }
      {
        $group: {
          _id: {
            year: { $year: '$ts' }
            month: { $month: '$ts' }
            day: { $dayOfMonth: '$ts' }
          }
          count: { $sum: 1 }
        }
      }
      {
        $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
      }
    ]

    result = await collection.aggregate(pipeline)

    # 转换为日期字符串映射
    statsMap = {}
    for item in result
      year = item._id.year
      month = String(item._id.month).padStart(2, '0')
      day = String(item._id.day).padStart(2, '0')
      dateStr = "#{year}-#{month}-#{day}"
      statsMap[dateStr] = item.count

    return statsMap
  catch error
    debug.error 'Error getting stats for collection:', error
    return {}

# 生成Excel文件
generateExcel = (statsData, outputPath) ->
  try
    # 生成日期范围
    startDate = new Date('2025-07-01')
    endDate = new Date('2025-08-31')
    dateRange = generateDateRange(startDate, endDate)

    # 创建表头
    headers = ['Date']
    for collection in COLLECTIONS
      headers.push(collection.name)

    # 创建数据行
    rows = [headers]

    for date in dateRange
      row = [date]
      for collection in COLLECTIONS
        count = statsData[collection.name]?[date] or 0
        row.push(count)
      rows.push(row)

    # 创建工作簿
    workbook = xlsx.utils.book_new()
    worksheet = xlsx.utils.aoa_to_sheet(rows)
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Daily Stats')

    # 写入文件
    xlsx.writeFile(workbook, outputPath)
    debug.info "Excel file generated successfully: #{outputPath}"

  catch error
    debug.error 'Error generating Excel file:', error
    throw error

# 主函数
main = ->
  debug.info 'Starting data collection for July and August 2025...'

  # 收集所有表的统计数据
  statsData = {}
  for collection in COLLECTIONS
    debug.info "Processing #{collection.name}..."
    stats = await getCollectionStats(collection.col)
    statsData[collection.name] = stats
    debug.info "#{collection.name} completed, found #{Object.keys(stats).length} days with data"

  # 生成输出文件路径
  outputPath = path.join('/opt/appd5/rni_daily_stats.xlsx')

  # 生成Excel文件
  await generateExcel(statsData, outputPath)

  debug.info 'Process completed successfully!'
  EXIT 0

main()